import 'dart:convert';
import 'package:firebase_remote_config/firebase_remote_config.dart';
import 'package:flutter/material.dart';
import 'package:gt_plus/models/gt_instructions_model.dart';

import '../../modules/appUpdate/package_info.dart';
import '../../utils/appConst/app_urls.dart';

class FirebaseRemoteConfigService {
  static final FirebaseRemoteConfigService _singleton =
      FirebaseRemoteConfigService._internal();
  final packageInfo = PackageInfoSetup();

  factory FirebaseRemoteConfigService() {
    return _singleton;
  }

  final remoteConfig = FirebaseRemoteConfig.instance;
  FirebaseRemoteConfigService._internal();

  Future<void> initialize() async {
    try {
      // Set configuration
      await remoteConfig.setConfigSettings(
        RemoteConfigSettings(
          fetchTimeout: const Duration(seconds: 15),
          minimumFetchInterval: const Duration(seconds: 1),
        ),
      );

      //createMinVersionKey();

      await remoteConfig.setDefaults({
        FirebaseRemoteConfigKey.timerInSecondsGTDev: 300,
        FirebaseRemoteConfigKey.timerInSecondsGTProd: 300,
        FirebaseRemoteConfigKey.isFlashNeededGT: false,
        FirebaseRemoteConfigKey.minVersionDev: "1.0.0",
        FirebaseRemoteConfigKey.minVersionProd: "1.0.0",
      });

      bool updated = await remoteConfig.fetchAndActivate();
      debugPrint("Remote config updated: $updated");
    } catch (e) {
      debugPrint("Error initializing Firebase Remote Config: $e");
    }
  }

  String getMinRequiredVersion() =>
      remoteConfig.getString(isProdEnv 
          ? FirebaseRemoteConfigKey.minVersionProd 
          : FirebaseRemoteConfigKey.minVersionDev);

  int getTimerInSeconds() {
    int timerInSec = remoteConfig.getInt(
      isProdEnv
          ? FirebaseRemoteConfigKey.timerInSecondsGTProd
          : FirebaseRemoteConfigKey.timerInSecondsGTDev,
    );
    return timerInSec;
  }

  bool isFlashNeeded() {
    return remoteConfig.getBool(
      FirebaseRemoteConfigKey.isFlashNeededGT,
    );
  }

  GTInstructionsModel getInstructions() {
    String jsonString =
        remoteConfig.getString(FirebaseRemoteConfigKey.instructionsGT);
    Map<String, dynamic> jsonData = jsonDecode(jsonString);
    GTInstructionsModel gtInstructionsModel =
        GTInstructionsModel.fromJson(jsonData);

    return gtInstructionsModel;
  }

  String getAudiometryPrompt() {
    return remoteConfig.getString(FirebaseRemoteConfigKey.audiometryPromptGT);
  }

  String getCognivuePrompt() {
    return remoteConfig.getString(FirebaseRemoteConfigKey.cognivuePromptGT);
  }

  String getQuestionnairesData() {
    return remoteConfig.getString(
      isProdEnv
          ? FirebaseRemoteConfigKey.questionnairesProductionGT
          : FirebaseRemoteConfigKey.questionnairesStagingGT,
    );
  }

  Map<String, dynamic>? getQualificationQuestion() {
    try {
      final jsonString = getQuestionnairesData();
      if (jsonString.isEmpty) {
        debugPrint('No questionnaire data found in remote config');
        return null;
      }

      final jsonData = jsonDecode(jsonString);
      final gtIpadConfig = jsonData['gt-ipad-config'];

      if (gtIpadConfig != null && gtIpadConfig['qualification'] != null) {
        return Map<String, dynamic>.from(gtIpadConfig['qualification']);
      }

      debugPrint('No qualification question found in remote config');
      return null;
    } catch (e) {
      debugPrint('Error parsing qualification question from remote config: $e');
      return null;
    }
  }
}

class FirebaseRemoteConfigKey {
  static const String timerInSecondsGTDev = "TIMER_IN_SECONDS_GT_DEV";
  static const String timerInSecondsGTProd = "TIMER_IN_SECONDS_GT_PROD";
  static const String instructionsGT = "INSTRUCTIONS_GT";
  static const String isFlashNeededGT = "IS_FLASH_NEEDED_GT";
  static const String audiometryPromptGT = "AUDIOMETRY_PROMPT_GT";
  static const String cognivuePromptGT = "COGNIVUE_PROMPT_GT";
  static const String minVersionDev = "MIN_VERSION_GT_DEV";
  static const String minVersionProd = "MIN_VERSION_GT_PROD";
  static const String questionnairesStagingGT = "QUESTIONNAIRES_STAGING";
  static const String questionnairesProductionGT = "QUESTIONNAIRES_PRODUCTION";
}
